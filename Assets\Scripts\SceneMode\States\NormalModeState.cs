using UnityEngine;
using QFramework;
using CommonEvent;
using QFramework.Example;
using UnityEngine.Rendering;
using SCPE;

/// <summary>
/// 正常浏览模式状态
/// Normal browsing mode state
/// </summary>
public class NormalModeState : SceneBaseState
{
    public override void EnterState(SceneStateManager sceneManager)
    {
        Debug.Log("进入正常浏览模式 - Enter Normal Mode");

        // 设置正常模式的基本配置
        // Set basic configuration for normal mode
        SetupNormalMode();

        // 启用正常模式功能
        // Enable normal mode features
        EnableNormalModeFeatures();

        // 处理UI面板切换
        // Handle UI panel switching
        HandleUIPanelSwitching();

        // 设置正常模式场景
        // Setup normal mode scene
        SetupNormalModeScene();
    }

    public override void UpdateState(SceneStateManager sceneManager)
    {
        // 正常模式的更新逻辑
        // Update logic for normal mode
        HandleNormalModeInput();
        UpdateNormalModeUI();
    }

    public override void ExitState(SceneStateManager sceneManager)
    {
        Debug.Log("退出正常浏览模式 - Exit Normal Mode");

        // 执行退出正常模式的流程
        // Execute exit normal mode process
        ExitNormalModeProcess();

        // 清理正常模式的设置
        // Clean up normal mode settings
        CleanupNormalMode();

        // 禁用正常模式功能
        // Disable normal mode features
        DisableNormalModeFeatures();
    }

    /// <summary>
    /// 设置正常模式
    /// Setup normal mode
    /// </summary>
    private void SetupNormalMode()
    {
        // 调用GameManager的初始化方法
        // Call GameManager's initialization method
        GameManager.Instance.init(EsceneModel._normalModel);

        // 启用后处理效果（如果需要）
        // Enable post processing effects if needed
        Volume myVolume = GameObject.Find("Global Volume").GetComponent<Volume>();
        if (myVolume != null && myVolume.profile != null)
        {
            // 正确的方式：使用SCPE.Fog而不是SCPE.FogRenderer
            // Correct way: use SCPE.Fog instead of SCPE.FogRenderer
            SCPE.Fog fogEffect;
            myVolume.profile.TryGet(typeof(SCPE.Fog), out fogEffect);

            if (fogEffect != null)
            {
                // 启用雾效果
                // Enable fog effect
                fogEffect.active = true;

                // // 设置雾效果参数（可选）
                // // Set fog effect parameters (optional)
                // fogEffect.globalDensity.overrideState = true;
                // fogEffect.globalDensity.value = 1.0f;

                // fogEffect.distanceFog.overrideState = true;
                // fogEffect.distanceFog.value = true;
            }
        }

        // myVolume.profile.TryGet<ShadowsMidtonesHighlights>(out shadowsMidtonesHighlights);
        // shadowsMidtonesHighlights.active = isOn;
        

        Debug.Log("设置正常浏览模式基本配置");
    }

    /// <summary>
    /// 启用正常模式功能
    /// Enable normal mode features
    /// </summary>
    private void EnableNormalModeFeatures()
    {
        // 禁用RTG应用
        // Disable RTG application
        if (GameManager.Instance.RTGApp != null)
        {
            GameManager.Instance.RTGApp.SetActive(false);
        }

        // 发送打开正常模式面板事件
        // Send open normal model panel event
        TypeEventSystem.Global.Send(new OpenMyNormalModelPanel());

        // 发送改变对话UI位置事件
        // Send change talk UI position event
        TypeEventSystem.Global.Send(new ChangeTalkUIPositon()
        {
            esceneModel = EsceneModel._normalModel,
        });

        // 设置灯光
        // Setup lighting
        if (GameObject.Find("Directional Light") != null)
        {
            GameObject.Find("Directional Light").GetComponent<Light>().enabled = true;
        }
        if (GameObject.Find("Directional Light (2)") != null)
        {
            GameObject.Find("Directional Light (2)").GetComponent<Light>().enabled = false;
        }

        // 发送返回正常模式事件（统一使用FloorBack事件）
        // Send back to normal model event (unified using FloorBack event)
        TypeEventSystem.Global.Send(new FloorBack());
        ModelManager.Instance.dixing.SetActive(true);

        Debug.Log("启用正常模式功能");
    }

    /// <summary>
    /// 处理UI面板切换
    /// Handle UI panel switching
    /// </summary>
    private void HandleUIPanelSwitching()
    {
        // 隐藏建筑系统面板
        // Hide build system panel
        if (UIKit.GetPanel<BuildSystemPanel>() != null)
        {
            UIKit.GetPanel<BuildSystemPanel>().gameObject.SetActive(false);
        }

        // 确保正常模式面板可用并显示UI元素
        // Ensure normal mode panel is available and show UI elements
        if (UIKit.GetPanel<NormalModelPanel>() != null)
        {
            var panel = UIKit.GetPanel<NormalModelPanel>();
            // 激活面板
            // Activate panel
            panel.gameObject.SetActive(true);

            // 显示正常模式UI元素
            // Show normal mode UI elements
            panel.HomePageDataImg.gameObject.SetActive(true);
            panel.LeftScaleBtn.gameObject.SetActive(true);

            // panel.TemperatureAndWetnessMonitoringBtn.gameObject.SetActive(false);
            // panel.TemperatureCloudBtn.gameObject.SetActive(false);
            // panel.DataDetectionBtn.gameObject.SetActive(false);
            panel.TopLeft.gameObject.SetActive(false);
            panel.TopRight.gameObject.SetActive(false);
            panel.DataDetectionBG.gameObject.SetActive(false);
            panel.currentState = NormalModelPanel.dataState.noState;
            panel.CtrInfo.gameObject.SetActive(false);
        }

        Debug.Log("处理UI面板切换");
    }

    /// <summary>
    /// 设置正常模式场景
    /// Setup normal mode scene
    /// </summary>
    private void SetupNormalModeScene()
    {
        // 设置AI狗的位置（正常模式位置）
        // Set AI dog position (normal mode position)
        if (ModelManager.Instance.AIDog != null)
        {
            ModelManager.Instance.AIDog.transform.position = new Vector3(709.56f, 807.07f, 10f);
        }

        // 设置房间模型的层级（正常模式层级）
        // Set room model layers (normal mode layers)
        if (ModelManager.Instance.Room != null && ModelManager.Instance.Room.transform.childCount > 0)
        {
            foreach (Transform child in ModelManager.Instance.Room.transform)
            {
                child.gameObject.layer = 0; // 默认层级
            }
        }

        // 只在程序启动时加载测试数据，不在状态切换时重复加载
        // Only load test data at startup, not during state switching
        if (ShouldLoadTestData())
        {
            Debug.Log("  -> 加载测试数据和生成楼层按钮");
            // LoadManager.Instance.TestLoadFromJson();
            
        }
        else
        {
            Debug.Log("  -> 跳过测试数据加载（楼层按钮已存在）");
        }
        ModelManager.Instance.Room = null;
        Debug.Log("设置正常模式场景");
    }

    /// <summary>
    /// 判断是否应该加载测试数据
    /// Determine if test data should be loaded
    /// </summary>
    private bool ShouldLoadTestData()
    {
        // 检查是否已经有楼层按钮（除了FloorBackBtn）
        // Check if floor buttons already exist (except FloorBackBtn)
        if (UIKit.GetPanel<NormalModelPanel>() != null)
        {
            var floorBG = UIKit.GetPanel<NormalModelPanel>().PrigramgsFloorBG.transform;
            int buttonCount = 0;

            for (int i = 0; i < floorBG.childCount; i++)
            {
                var child = floorBG.GetChild(i);
                if (child.name != "FloorBackBtn")
                {
                    buttonCount++;
                }
            }

            // 如果已经有楼层按钮，就不需要重新加载
            // If floor buttons already exist, no need to reload
            return buttonCount == 0;
        }

        return true;
    }

    /// <summary>
    /// 处理正常模式输入
    /// Handle normal mode input
    /// </summary>
    private void HandleNormalModeInput()
    {
        // 这里可以添加正常模式特有的输入处理逻辑
        // Add normal mode specific input handling logic here

        // 例如：相机控制、物体选择、UI交互等
        // Example: Camera control, object selection, UI interaction, etc.
    }

    /// <summary>
    /// 更新正常模式UI
    /// Update normal mode UI
    /// </summary>
    private void UpdateNormalModeUI()
    {
        // 这里可以添加正常模式UI的更新逻辑
        // Add normal mode UI update logic here

        // 例如：更新信息面板、状态显示等
        // Example: Update info panels, status displays, etc.
    }

    /// <summary>
    /// 执行退出正常模式的流程
    /// Execute exit normal mode process
    /// </summary>
    private void ExitNormalModeProcess()
    {
        // 这里可以添加退出正常模式时的特殊处理
        // Add special handling when exiting normal mode

        Debug.Log("执行退出正常模式流程");
    }

    /// <summary>
    /// 清理正常模式
    /// Cleanup normal mode
    /// </summary>
    private void CleanupNormalMode()
    {
        // 这里可以添加正常模式的清理逻辑
        // Add normal mode cleanup logic here
        ModelManager.Instance.dixing.SetActive(false);
          Volume myVolume = GameObject.Find("Global Volume").GetComponent<Volume>();
        if (myVolume != null && myVolume.profile != null)
        {
            // 正确的方式：使用SCPE.Fog而不是SCPE.FogRenderer
            // Correct way: use SCPE.Fog instead of SCPE.FogRenderer
            SCPE.Fog fogEffect;
            myVolume.profile.TryGet(typeof(SCPE.Fog), out fogEffect);

            if (fogEffect != null)
            {
                // 启用雾效果
                // Enable fog effect
                fogEffect.active = false;

                // // 设置雾效果参数（可选）
                // // Set fog effect parameters (optional)
                // fogEffect.globalDensity.overrideState = true;
                // fogEffect.globalDensity.value = 1.0f;

                // fogEffect.distanceFog.overrideState = true;
                // fogEffect.distanceFog.value = true;
            }
        }

        Debug.Log("清理正常浏览模式");
    }

    /// <summary>
    /// 禁用正常模式功能
    /// Disable normal mode features
    /// </summary>
    private void DisableNormalModeFeatures()
    {
        // 这里可以添加需要禁用的正常模式功能
        // Add normal mode features that need to be disabled

        Debug.Log("禁用正常模式功能");
    }
}
